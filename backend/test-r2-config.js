/**
 * 测试 Cloudflare R2 配置的脚本
 * 运行此脚本来验证 R2 配置是否正确
 */

const { loadEnv } = require("@medusajs/framework/utils");

// 加载环境变量
loadEnv(process.env.NODE_ENV, process.cwd());

function testR2Configuration() {
  console.log("🔍 检查 Cloudflare R2 配置...\n");

  const requiredVars = [
    'R2_BUCKET',
    'R2_ENDPOINT', 
    'R2_ACCESS_KEY',
    'R2_SECRET_KEY',
    'R2_PUBLIC_URL'
  ];

  const optionalVars = [
    'R2_CACHE_CONTROL',
    'R2_PRESIGNED_URL_EXPIRES'
  ];

  let hasErrors = false;

  // 检查必需的环境变量
  console.log("📋 必需的环境变量:");
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`);
    } else {
      console.log(`❌ ${varName}: 未设置`);
      hasErrors = true;
    }
  });

  console.log("\n📋 可选的环境变量:");
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`⚠️  ${varName}: 未设置（将使用默认值）`);
    }
  });

  // 验证配置格式
  console.log("\n🔍 验证配置格式:");
  
  if (process.env.R2_ENDPOINT) {
    if (process.env.R2_ENDPOINT.startsWith('https://') && process.env.R2_ENDPOINT.includes('.r2.cloudflarestorage.com')) {
      console.log("✅ R2_ENDPOINT 格式正确");
    } else {
      console.log("❌ R2_ENDPOINT 格式可能不正确，应该类似: https://account-id.r2.cloudflarestorage.com");
      hasErrors = true;
    }
  }

  if (process.env.R2_PUBLIC_URL) {
    if (process.env.R2_PUBLIC_URL.startsWith('https://')) {
      console.log("✅ R2_PUBLIC_URL 格式正确");
    } else {
      console.log("❌ R2_PUBLIC_URL 应该以 https:// 开头");
      hasErrors = true;
    }
  }

  if (process.env.R2_PRESIGNED_URL_EXPIRES) {
    const expires = parseInt(process.env.R2_PRESIGNED_URL_EXPIRES);
    if (!isNaN(expires) && expires > 0) {
      console.log("✅ R2_PRESIGNED_URL_EXPIRES 格式正确");
    } else {
      console.log("❌ R2_PRESIGNED_URL_EXPIRES 应该是一个正整数");
      hasErrors = true;
    }
  }

  // 显示配置摘要
  console.log("\n📊 配置摘要:");
  console.log(`存储桶: ${process.env.R2_BUCKET || '未设置'}`);
  console.log(`端点: ${process.env.R2_ENDPOINT || '未设置'}`);
  console.log(`公共URL: ${process.env.R2_PUBLIC_URL || '未设置'}`);
  console.log(`缓存控制: ${process.env.R2_CACHE_CONTROL || 'max-age=31536000 (默认)'}`);
  console.log(`预签名URL过期时间: ${process.env.R2_PRESIGNED_URL_EXPIRES || '3600 (默认)'} 秒`);

  // 最终结果
  console.log("\n" + "=".repeat(50));
  if (hasErrors) {
    console.log("❌ 配置检查失败！请修复上述错误后重试。");
    console.log("\n💡 提示:");
    console.log("1. 确保已创建 .env 文件并设置了所有必需的 R2 环境变量");
    console.log("2. 参考 .env.template 文件中的示例配置");
    console.log("3. 查看 docs/cloudflare-r2-setup.md 获取详细配置指南");
    process.exit(1);
  } else {
    console.log("✅ 配置检查通过！R2 配置看起来正确。");
    console.log("\n🚀 下一步:");
    console.log("1. 启动 Medusa 开发服务器: npm run dev");
    console.log("2. 访问管理后台测试文件上传功能");
    console.log("3. 检查文件是否正确上传到 R2 存储桶");
  }
}

// 运行测试
testR2Configuration();
