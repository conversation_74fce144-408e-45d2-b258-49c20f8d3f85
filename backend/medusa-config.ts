import { defineConfig, loadEnv, Modules } from "@medusajs/framework/utils";

loadEnv(process.env.NODE_ENV, process.cwd());

export default defineConfig({
  projectConfig: {
    redisUrl: process.env.REDIS_URL,
    databaseUrl: process.env.DATABASE_URL,
    databaseLogging: true,
    http: {
      storeCors: process.env.STORE_CORS,
      adminCors: process.env.ADMIN_CORS,
      authCors: process.env.AUTH_CORS,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
    },
  },
  admin: {
    backendUrl: process.env.MEDUSA_ADMIN_BACKEND_URL || "http://localhost:9000",
  },
  modules: [
    {
      resolve: "./modules/sanity",
      options: {
        api_token: process.env.SANITY_API_TOKEN,
        project_id: process.env.SANITY_PROJECT_ID,
        api_version: new Date().toISOString().split("T")[0],
        dataset: "production",
        studio_url: "https://munchies-tinloof.vercel.app/cms",
        type_map: {
          collection: "collection",
          category: "category",
          product: "product",
        },
      },
    },
    {
      resolve: "@medusajs/file",
      key: Modules.FILE,
      options: {
        providers: [
          // 本地文件存储（开发环境）
          {
            resolve: "@medusajs/medusa/file-local",
            id: "local",
            options: {
              upload_dir: "uploads",
              backend_url: (process.env.BACKEND_URL || "http://localhost:9000") + "/uploads",
            },
          },
          // Cloudflare R2 存储（生产环境，需要配置环境变量）
          ...(process.env.R2_BUCKET ? [{
            resolve: "@agilo/medusa-file-r2",
            id: "r2",
            options: {
              bucket: process.env.R2_BUCKET,
              endpoint: process.env.R2_ENDPOINT,
              access_key: process.env.R2_ACCESS_KEY,
              secret_key: process.env.R2_SECRET_KEY,
              public_url: process.env.R2_PUBLIC_URL,
              // 可选配置
              cache_control: process.env.R2_CACHE_CONTROL || "max-age=31536000",
              presigned_url_expires: parseInt(process.env.R2_PRESIGNED_URL_EXPIRES || "3600"), // 1小时
            },
          }] : []),
        ],
      },
    },
    {
      resolve: "@medusajs/medusa/payment",
      key: Modules.PAYMENT,
      options: {
        providers: [
          {
            resolve: "@medusajs/medusa/payment-stripe",
            id: "stripe",
            options: {
              apiKey: process.env.STRIPE_API_KEY,
            },
          },
        ],
      },
    },
  ],
});
