# 环境变量参考指南

本文档详细列出了 Munchies 项目中所有环境变量的配置说明，包括后端、前端和第三方服务的配置。

## 目录
- [后端环境变量](#后端环境变量)
- [前端环境变量](#前端环境变量)
- [第三方服务配置](#第三方服务配置)
- [环境变量模板](#环境变量模板)
- [安全最佳实践](#安全最佳实践)

## 后端环境变量

### 基础配置

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `NODE_ENV` | ✅ | - | 运行环境 (`development`, `production`) |
| `DATABASE_URL` | ✅ | - | PostgreSQL 数据库连接字符串 |
| `REDIS_URL` | ✅ | - | Redis 连接字符串 |
| `JWT_SECRET` | ✅ | `supersecret` | JWT 令牌签名密钥 |
| `COOKIE_SECRET` | ✅ | `supersecret` | Cookie 签名密钥 |
| `BACKEND_URL` | ✅ | - | 后端服务的完整 URL |

#### 示例配置
```bash
NODE_ENV=production
DATABASE_URL=************************************/medusa_store
REDIS_URL=redis://user:password@host:6379
JWT_SECRET=your-super-secure-jwt-secret-here
COOKIE_SECRET=your-super-secure-cookie-secret-here
BACKEND_URL=https://your-backend.medusajs.app
```

### CORS 配置

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `STORE_CORS` | ✅ | 允许访问商店 API 的域名列表 |
| `ADMIN_CORS` | ✅ | 允许访问管理 API 的域名列表 |
| `AUTH_CORS` | ✅ | 允许访问认证 API 的域名列表 |

#### 示例配置
```bash
STORE_CORS=https://your-storefront.com,https://your-domain.com
ADMIN_CORS=https://your-admin.com,https://your-backend.medusajs.app
AUTH_CORS=https://your-admin.com,https://your-backend.medusajs.app
```

### 数据库配置

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `DATABASE_URL` | ✅ | 完整的 PostgreSQL 连接字符串 |
| `POSTGRES_URL` | ❌ | 备用 PostgreSQL 连接字符串 |
| `DB_NAME` | ❌ | 数据库名称（用于某些部署平台） |

#### 连接字符串格式
```bash
# 标准格式
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# 带 SSL 的格式
DATABASE_URL=postgresql://username:password@hostname:port/database_name?sslmode=require

# 连接池配置
DATABASE_URL=postgresql://username:password@hostname:port/database_name?pool_max=20
```

### 文件存储配置 (Cloudflare R2)

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `R2_BUCKET` | ✅ | R2 存储桶名称 |
| `R2_ENDPOINT` | ✅ | R2 API 端点 |
| `R2_ACCESS_KEY` | ✅ | Cloudflare R2 访问密钥 |
| `R2_SECRET_KEY` | ✅ | Cloudflare R2 秘密密钥 |
| `R2_PUBLIC_URL` | ✅ | R2 存储桶的公共访问 URL |
| `R2_CACHE_CONTROL` | ❌ | 缓存控制头（默认：max-age=********） |
| `R2_PRESIGNED_URL_EXPIRES` | ❌ | 预签名 URL 过期时间（秒，默认：3600） |

#### 示例配置
```bash
R2_BUCKET=your-medusa-files
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-r2-access-key
R2_SECRET_KEY=your-r2-secret-key
R2_PUBLIC_URL=https://files.yourdomain.com
R2_CACHE_CONTROL=max-age=********
R2_PRESIGNED_URL_EXPIRES=3600
```

## 前端环境变量

### Medusa 集成

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `NEXT_PUBLIC_MEDUSA_BACKEND_URL` | ✅ | Medusa 后端 API URL (包含 `/store`) |
| `NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY` | ✅ | Medusa 可发布密钥 |

#### 示例配置
```bash
NEXT_PUBLIC_MEDUSA_BACKEND_URL=https://your-backend.medusajs.app/store
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_01234567890abcdef
```

### Sanity CMS 配置

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `NEXT_PUBLIC_SANITY_PROJECT_ID` | ✅ | Sanity 项目 ID |
| `NEXT_PUBLIC_SANITY_DATASET` | ✅ | Sanity 数据集名称 |
| `NEXT_PUBLIC_SANITY_API_VERSION` | ✅ | Sanity API 版本 (YYYY-MM-DD 格式) |
| `SANITY_API_TOKEN` | ✅ | Sanity API 令牌 (服务端使用) |
| `SANITY_REVALIDATE_SECRET` | ❌ | 重新验证密钥 |

#### 示例配置
```bash
NEXT_PUBLIC_SANITY_PROJECT_ID=qqvccmla
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=2025-08-12
SANITY_API_TOKEN=sk_your_sanity_token_here
SANITY_REVALIDATE_SECRET=your_revalidate_secret
```

### 支付配置

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `NEXT_PUBLIC_STRIPE_KEY` | ✅ | Stripe 可发布密钥 |

#### 示例配置
```bash
# 测试环境
NEXT_PUBLIC_STRIPE_KEY=pk_test_51234567890abcdef

# 生产环境
NEXT_PUBLIC_STRIPE_KEY=pk_live_51234567890abcdef
```

## 第三方服务配置

### Stripe 支付

#### 后端配置
| 变量名 | 必需 | 说明 |
|--------|------|------|
| `STRIPE_API_KEY` | ✅ | Stripe 秘密密钥 |

```bash
# 测试环境
STRIPE_API_KEY=sk_test_51234567890abcdef

# 生产环境
STRIPE_API_KEY=sk_live_51234567890abcdef
```

### Sanity CMS

#### 后端配置
| 变量名 | 必需 | 说明 |
|--------|------|------|
| `SANITY_API_TOKEN` | ✅ | Sanity API 令牌 |
| `SANITY_PROJECT_ID` | ✅ | Sanity 项目 ID |
| `SANITY_ORGANIZATION_ID` | ❌ | Sanity 组织 ID |

```bash
SANITY_API_TOKEN=sk_your_sanity_token_here
SANITY_PROJECT_ID=qqvccmla
SANITY_ORGANIZATION_ID=your_org_id
```

### 邮件服务 (Resend)

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `RESEND_API_KEY` | ❌ | Resend 邮件服务 API 密钥 |
| `MEDUSA_PUBLISHABLE_KEY` | ❌ | 用于管理扩展的 Medusa 可发布密钥 |

```bash
RESEND_API_KEY=re_your_resend_api_key
MEDUSA_PUBLISHABLE_KEY=pk_your_medusa_publishable_key
```

## 环境变量模板

### 后端 (.env)
```bash
# 基础配置
NODE_ENV=production
DATABASE_URL=postgresql://username:password@host:port/database
REDIS_URL=redis://username:password@host:port
JWT_SECRET=your-super-secure-jwt-secret-here
COOKIE_SECRET=your-super-secure-cookie-secret-here
BACKEND_URL=https://your-backend.medusajs.app

# CORS 配置
STORE_CORS=https://your-storefront.com
ADMIN_CORS=https://your-admin.com
AUTH_CORS=https://your-admin.com

# 文件存储 (Cloudflare R2)
R2_BUCKET=your-bucket-name
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_PUBLIC_URL=https://files.yourdomain.com
R2_CACHE_CONTROL=max-age=********
R2_PRESIGNED_URL_EXPIRES=3600

# Sanity 集成
SANITY_API_TOKEN=sk_your_sanity_token_here
SANITY_PROJECT_ID=your_project_id
SANITY_ORGANIZATION_ID=your_org_id

# 支付 (Stripe)
STRIPE_API_KEY=sk_your_stripe_secret_key

# 邮件服务 (可选)
RESEND_API_KEY=re_your_resend_api_key
MEDUSA_PUBLISHABLE_KEY=pk_your_medusa_publishable_key
```

### 前端 (.env)
```bash
# Medusa 后端
NEXT_PUBLIC_MEDUSA_BACKEND_URL=https://your-backend.medusajs.app/store
NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY=pk_your_publishable_key

# Sanity CMS
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_API_VERSION=2025-08-12
SANITY_API_TOKEN=sk_your_sanity_token_here
SANITY_REVALIDATE_SECRET=your_revalidate_secret

# 支付 (Stripe)
NEXT_PUBLIC_STRIPE_KEY=pk_your_stripe_publishable_key

# 其他配置
NODE_ENV=production
```

## 环境特定配置

### 开发环境
```bash
# 后端
NODE_ENV=development
DATABASE_URL=postgresql://localhost:5432/medusa_dev
REDIS_URL=redis://localhost:6379
BACKEND_URL=http://localhost:9000
STORE_CORS=http://localhost:3000
ADMIN_CORS=http://localhost:7000,http://localhost:7001

# 前端
NEXT_PUBLIC_MEDUSA_BACKEND_URL=http://localhost:9000/store
NEXT_PUBLIC_SANITY_DATASET=development
```

### 测试环境
```bash
# 使用测试数据库和服务
DATABASE_URL=postgresql://localhost:5432/medusa_test
STRIPE_API_KEY=sk_test_your_test_key
NEXT_PUBLIC_STRIPE_KEY=pk_test_your_test_key
```

### 生产环境
```bash
# 使用生产服务和密钥
NODE_ENV=production
DATABASE_URL=postgresql://prod-host:5432/medusa_prod
STRIPE_API_KEY=sk_live_your_live_key
NEXT_PUBLIC_STRIPE_KEY=pk_live_your_live_key
```

## 安全最佳实践

### 1. 密钥管理
- ✅ 使用强随机密钥
- ✅ 定期轮换敏感密钥
- ✅ 不在代码中硬编码密钥
- ✅ 使用环境变量管理工具

### 2. 访问控制
- ✅ 使用最小权限原则
- ✅ 分离开发和生产环境
- ✅ 限制 API 密钥权限
- ✅ 启用 API 密钥监控

### 3. 网络安全
- ✅ 配置正确的 CORS 策略
- ✅ 使用 HTTPS
- ✅ 限制数据库访问
- ✅ 启用防火墙规则

### 4. 监控和审计
- ✅ 监控 API 使用情况
- ✅ 记录访问日志
- ✅ 设置异常告警
- ✅ 定期安全审计

## 故障排除

### 常见问题

1. **环境变量未生效**
   ```bash
   # 检查变量是否正确设置
   echo $DATABASE_URL
   
   # 重启应用服务
   # 清除构建缓存
   ```

2. **CORS 错误**
   ```bash
   # 检查 CORS 配置
   # 确保域名格式正确（包含协议）
   # 不要在末尾添加斜杠
   ```

3. **数据库连接失败**
   ```bash
   # 检查连接字符串格式
   # 验证数据库服务状态
   # 确认网络连接
   ```

### 调试技巧

1. **验证环境变量**
   ```javascript
   // 在代码中临时添加
   console.log('Environment variables:', {
     DATABASE_URL: process.env.DATABASE_URL ? '***' : 'undefined',
     REDIS_URL: process.env.REDIS_URL ? '***' : 'undefined',
   });
   ```

2. **测试连接**
   ```bash
   # 测试数据库连接
   psql $DATABASE_URL -c "SELECT 1;"
   
   # 测试 Redis 连接
   redis-cli -u $REDIS_URL ping
   ```

---

## 相关文档
- [Medusa Cloud 部署指南](./medusa-cloud-deployment.md)
- [Sanity 部署指南](./sanity-deployment.md)
- [前端部署指南](./storefront-deployment.md)
