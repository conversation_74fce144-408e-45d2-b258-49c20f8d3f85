# Cloudflare R2 配置指南

本文档详细介绍如何配置 Cloudflare R2 对象存储作为 Medusa 项目的文件存储解决方案。

## 目录
- [前置要求](#前置要求)
- [创建 Cloudflare R2 存储桶](#创建-cloudflare-r2-存储桶)
- [获取 API 凭证](#获取-api-凭证)
- [配置自定义域名](#配置自定义域名)
- [环境变量配置](#环境变量配置)
- [测试配置](#测试配置)
- [故障排除](#故障排除)

## 前置要求

1. **Cloudflare 账户**
   - 访问 [Cloudflare](https://cloudflare.com) 注册账户
   - 完成账户验证

2. **域名（推荐）**
   - 用于配置自定义域名访问 R2 存储桶
   - 避免 r2.dev 域名的速率限制

## 创建 Cloudflare R2 存储桶

### 1. 登录 Cloudflare Dashboard
1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 使用您的账户登录

### 2. 导航到 R2 Object Storage
1. 在左侧菜单中找到 "R2 Object Storage"
2. 如果是首次使用，需要启用 R2 服务

### 3. 创建存储桶
1. 点击 "Create bucket" 按钮
2. 输入存储桶名称（例如：`medusa-files`）
3. 选择存储桶位置（推荐选择离用户最近的区域）
4. 点击 "Create bucket" 完成创建

## 获取 API 凭证

### 1. 创建 R2 API Token
1. 在 Cloudflare Dashboard 中，点击右上角的用户头像
2. 选择 "My Profile"
3. 转到 "API Tokens" 标签页
4. 点击 "Create Token"

### 2. 配置 Token 权限
1. 选择 "Custom token" 模板
2. 设置以下权限：
   ```
   Zone:Zone Settings:Read
   Zone:Zone:Read
   Account:Cloudflare R2:Edit
   ```
3. 在 "Account Resources" 中选择您的账户
4. 在 "Zone Resources" 中选择相关域名（如果使用自定义域名）
5. 点击 "Continue to summary" 然后 "Create Token"

### 3. 记录重要信息
创建 Token 后，记录以下信息：
- **Account ID**: 在 Dashboard 右侧边栏可以找到
- **Access Key ID**: 创建的 API Token
- **Secret Access Key**: Token 的密钥部分

## 配置自定义域名

### 1. 添加自定义域名
1. 在 R2 存储桶详情页面，点击 "Settings" 标签
2. 在 "Custom Domains" 部分，点击 "Connect Domain"
3. 输入您的域名（例如：`files.yourdomain.com`）
4. 按照提示完成 DNS 配置

### 2. 配置 DNS 记录
在您的域名 DNS 设置中添加 CNAME 记录：
```
Type: CNAME
Name: files
Target: [R2提供的目标地址]
```

### 3. 启用 HTTPS
Cloudflare 会自动为自定义域名提供 SSL 证书，确保启用 HTTPS。

## 环境变量配置

在您的 `.env` 文件中添加以下配置：

```bash
# Cloudflare R2 配置
R2_BUCKET=medusa-files
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-r2-access-key
R2_SECRET_KEY=your-r2-secret-key
R2_PUBLIC_URL=https://files.yourdomain.com
R2_CACHE_CONTROL=max-age=********
R2_PRESIGNED_URL_EXPIRES=3600
```

### 配置说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `R2_BUCKET` | R2 存储桶名称 | `medusa-files` |
| `R2_ENDPOINT` | R2 API 端点 | `https://abc123.r2.cloudflarestorage.com` |
| `R2_ACCESS_KEY` | R2 访问密钥 | `your-access-key` |
| `R2_SECRET_KEY` | R2 秘密密钥 | `your-secret-key` |
| `R2_PUBLIC_URL` | 公共访问 URL | `https://files.yourdomain.com` |
| `R2_CACHE_CONTROL` | 缓存控制头 | `max-age=********` |
| `R2_PRESIGNED_URL_EXPIRES` | 预签名 URL 过期时间（秒） | `3600` |

## 测试配置

### 1. 启动开发服务器
```bash
cd backend
npm run dev
```

### 2. 测试文件上传
1. 访问 Medusa Admin Dashboard
2. 尝试上传产品图片或其他文件
3. 检查文件是否成功上传到 R2 存储桶

### 3. 验证文件访问
1. 检查上传的文件是否可以通过公共 URL 访问
2. 确认文件 URL 格式正确

## 故障排除

### 常见问题

#### 1. 上传失败
**问题**: 文件上传时出现错误
**解决方案**:
- 检查 R2 API 凭证是否正确
- 确认存储桶名称拼写正确
- 验证 R2_ENDPOINT 格式是否正确

#### 2. 文件无法访问
**问题**: 上传的文件无法通过 URL 访问
**解决方案**:
- 检查 R2_PUBLIC_URL 配置
- 确认自定义域名 DNS 配置正确
- 验证存储桶的公共访问权限

#### 3. CORS 错误
**问题**: 前端访问文件时出现 CORS 错误
**解决方案**:
- 在 R2 存储桶设置中配置 CORS 策略
- 添加允许的源域名和请求方法

### 调试步骤

1. **检查环境变量**
   ```bash
   echo $R2_BUCKET
   echo $R2_ENDPOINT
   echo $R2_PUBLIC_URL
   ```

2. **查看应用日志**
   ```bash
   npm run dev
   # 查看控制台输出的错误信息
   ```

3. **测试 R2 连接**
   使用 AWS CLI 或其他 S3 兼容工具测试连接：
   ```bash
   aws s3 ls --endpoint-url=$R2_ENDPOINT
   ```

## 成本优化

### R2 定价优势
- **免费额度**: 每月 10GB 存储空间
- **无出站费用**: 从 R2 到互联网的数据传输免费
- **低存储成本**: $0.015/GB/月

### 优化建议
1. 使用自定义域名避免 r2.dev 速率限制
2. 配置适当的缓存控制头
3. 定期清理不需要的文件
4. 使用 Cloudflare CDN 加速全球访问

## 迁移现有文件

如果您之前使用 AWS S3，可以使用以下方法迁移文件：

### 1. 使用 rclone
```bash
# 安装 rclone
curl https://rclone.org/install.sh | sudo bash

# 配置 S3 和 R2 连接
rclone config

# 同步文件
rclone sync s3:old-bucket r2:new-bucket
```

### 2. 使用 AWS CLI
```bash
# 从 S3 下载
aws s3 sync s3://old-bucket ./temp-files

# 上传到 R2
aws s3 sync ./temp-files s3://new-bucket --endpoint-url=$R2_ENDPOINT
```

## 安全最佳实践

1. **API 密钥管理**
   - 定期轮换 API 密钥
   - 使用最小权限原则
   - 不要在代码中硬编码密钥

2. **访问控制**
   - 配置适当的存储桶策略
   - 使用预签名 URL 控制访问时间
   - 启用访问日志记录

3. **数据保护**
   - 启用版本控制（如需要）
   - 配置备份策略
   - 考虑启用加密

通过以上配置，您的 Medusa 项目现在将使用 Cloudflare R2 作为文件存储解决方案，享受更好的性能和成本效益。
