"use client";

import { useEffect, useState } from "react";

/**
 * 自定义hook用于处理轮播文字动画
 * @param currentIndex 当前轮播索引
 * @returns 动画状态对象
 */
export function useCarouselTextAnimation(currentIndex: number) {
  const [animationKey, setAnimationKey] = useState(0);

  useEffect(() => {
    // 每次索引变化时更新动画key，强制重新渲染
    setAnimationKey(prev => prev + 1);
  }, [currentIndex]);

  return {
    animationKey,
    // 使用自定义CSS动画类
    getAnimatedClasses: (delay: number = 0) => {
      const delayClass = delay > 0 ? `animation-delay-${delay}` : "";
      return [
        "animate-slide-up-fade",
        delayClass
      ].filter(Boolean).join(" ");
    },
  };
}

/**
 * 获取文字动画的CSS类名
 * @param isActive 是否为当前活动项
 * @param delay 延迟时间（0-400）
 * @returns CSS类名字符串
 */
export function getTextAnimationClasses(isActive: boolean, delay: number = 0): string {
  const baseClasses = "text-fade-transition";
  const stateClasses = isActive 
    ? "text-slide-up-enter-active" 
    : "text-slide-up-exit-active";
  const delayClass = delay > 0 ? `text-delay-${delay}` : "";
  
  return [baseClasses, stateClasses, delayClass].filter(Boolean).join(" ");
}
