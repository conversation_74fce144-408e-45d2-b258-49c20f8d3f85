/**
 * 测试价格验证逻辑的工具
 * 用于调试价格相关问题
 */

import { validateVariantPriceFromObject, canAddToCart, getVariantDisplayPrice } from './validate-variant-price';

// 模拟不同的变体数据结构进行测试
export function testPriceValidation() {
  console.log('🧪 开始测试价格验证逻辑...\n');

  // 测试用例1: 有 calculated_price 的变体
  const variantWithCalculatedPrice = {
    id: 'test-variant-1',
    calculated_price: {
      calculated_amount: 2599,
      currency_code: 'usd',
      formatted_amount: '$25.99',
    },
  };

  console.log('测试用例1: 有 calculated_price 的变体');
  console.log('输入:', JSON.stringify(variantWithCalculatedPrice, null, 2));
  console.log('canAddToCart:', canAddToCart(variantWithCalculatedPrice));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variantWithCalculatedPrice));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variantWithCalculatedPrice));
  console.log('---\n');

  // 测试用例2: 有 prices 数组的变体
  const variantWithPricesArray = {
    id: 'test-variant-2',
    prices: [
      {
        amount: 2999,
        currency_code: 'usd',
      },
    ],
  };

  console.log('测试用例2: 有 prices 数组的变体');
  console.log('输入:', JSON.stringify(variantWithPricesArray, null, 2));
  console.log('canAddToCart:', canAddToCart(variantWithPricesArray));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variantWithPricesArray));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variantWithPricesArray));
  console.log('---\n');

  // 测试用例3: 有直接 price 对象的变体
  const variantWithDirectPrice = {
    id: 'test-variant-3',
    price: {
      amount: 3499,
      currency_code: 'usd',
    },
  };

  console.log('测试用例3: 有直接 price 对象的变体');
  console.log('输入:', JSON.stringify(variantWithDirectPrice, null, 2));
  console.log('canAddToCart:', canAddToCart(variantWithDirectPrice));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variantWithDirectPrice));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variantWithDirectPrice));
  console.log('---\n');

  // 测试用例4: 没有价格的变体
  const variantWithoutPrice = {
    id: 'test-variant-4',
    title: 'No Price Variant',
  };

  console.log('测试用例4: 没有价格的变体');
  console.log('输入:', JSON.stringify(variantWithoutPrice, null, 2));
  console.log('canAddToCart:', canAddToCart(variantWithoutPrice));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variantWithoutPrice));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variantWithoutPrice));
  console.log('---\n');

  // 测试用例5: 价格为0的变体
  const variantWithZeroPrice = {
    id: 'test-variant-5',
    calculated_price: {
      calculated_amount: 0,
      currency_code: 'usd',
      formatted_amount: '$0.00',
    },
  };

  console.log('测试用例5: 价格为0的变体');
  console.log('输入:', JSON.stringify(variantWithZeroPrice, null, 2));
  console.log('canAddToCart:', canAddToCart(variantWithZeroPrice));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variantWithZeroPrice));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variantWithZeroPrice));
  console.log('---\n');

  // 测试用例6: 空的 prices 数组
  const variantWithEmptyPrices = {
    id: 'test-variant-6',
    prices: [],
  };

  console.log('测试用例6: 空的 prices 数组');
  console.log('输入:', JSON.stringify(variantWithEmptyPrices, null, 2));
  console.log('canAddToCart:', canAddToCart(variantWithEmptyPrices));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variantWithEmptyPrices));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variantWithEmptyPrices));
  console.log('---\n');

  console.log('✅ 价格验证逻辑测试完成');
}

// 测试真实的变体数据
export function testRealVariantData(variant: any) {
  console.log('🔍 测试真实变体数据...\n');
  console.log('变体ID:', variant?.id);
  console.log('变体标题:', variant?.title);
  console.log('原始数据:', JSON.stringify(variant, null, 2));
  console.log('---');
  console.log('canAddToCart:', canAddToCart(variant));
  console.log('getVariantDisplayPrice:', getVariantDisplayPrice(variant));
  console.log('validateVariantPriceFromObject:', validateVariantPriceFromObject(variant));
  console.log('---\n');
}

// 在浏览器控制台中使用的全局函数
if (typeof window !== 'undefined') {
  (window as any).testPriceValidation = testPriceValidation;
  (window as any).testRealVariantData = testRealVariantData;
}
