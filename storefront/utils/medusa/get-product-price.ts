import type {HttpTypes, StoreProductVariant} from "@medusajs/types";

import {getPercentageDiff} from "./get-precentage-diff";
import {convertToLocale} from "./money";

export const getPricesForVariant = (variant: StoreProductVariant) => {
  // 首先尝试使用 calculated_price
  if (variant?.calculated_price?.calculated_amount) {
    return {
      calculated_price: convertToLocale({
        amount: variant.calculated_price.calculated_amount,
        currency_code: variant.calculated_price.currency_code!,
      }),
      calculated_price_number: variant.calculated_price.calculated_amount,
      currency_code: variant.calculated_price.currency_code,
      original_price: convertToLocale({
        amount: variant.calculated_price.original_amount!,
        currency_code: variant.calculated_price.currency_code!,
      }),
      original_price_number: variant.calculated_price.original_amount,
      percentage_diff: getPercentageDiff(
        variant.calculated_price.original_amount!,
        variant.calculated_price.calculated_amount,
      ),
      price_type: variant.calculated_price.calculated_price?.price_list_type,
    };
  }

  // 回退：使用原始价格数据（当 calculated_price 失败时）
  const fallbackPrice = (variant as any)?.prices?.[0];
  if (fallbackPrice?.amount) {
    const amount = fallbackPrice.amount;
    const currencyCode = fallbackPrice.currency_code || 'usd';

    console.warn(`Using fallback price for variant ${variant?.id}: $${(amount / 100).toFixed(2)} - This variant cannot be added to cart due to Medusa v2 price calculation issues`);

    return {
      calculated_price: convertToLocale({
        amount: amount,
        currency_code: currencyCode,
      }),
      calculated_price_number: amount,
      currency_code: currencyCode,
      original_price: convertToLocale({
        amount: amount,
        currency_code: currencyCode,
      }),
      original_price_number: amount,
      percentage_diff: 0,
      price_type: 'fallback',
      is_fallback: true, // 标记这是fallback价格，不能添加到购物车
    };
  }

  // 如果没有价格数据，返回 null（现在应该有正确的价格计算了）
  console.warn(`No price found for variant ${variant?.id}`);
  return null;
};

export function getProductPrice({
  product,
  variantId,
}: {
  product: Pick<HttpTypes.StoreProduct, "id" | "variants">;
  variantId?: string;
}) {
  if (!product || !product.id) {
    throw new Error("No product provided");
  }

  const cheapestPrice = () => {
    if (!product || !product.variants?.length) {
      return null;
    }

    // 首先尝试使用有 calculated_price 的变体
    let cheapestVariant: any = product.variants
      .filter((v: any) => !!v.calculated_price?.calculated_amount)
      .sort((a: any, b: any) => {
        return (
          a.calculated_price.calculated_amount -
          b.calculated_price.calculated_amount
        );
      })[0];

    // 如果没有找到有 calculated_price 的变体，使用第一个变体
    if (!cheapestVariant) {
      cheapestVariant = product.variants[0];
    }

    return getPricesForVariant(cheapestVariant);
  };

  const variantPrice = () => {
    if (!product || !variantId) {
      return null;
    }

    const variant: any = product.variants?.find(
      (v) => v.id === variantId || v.sku === variantId,
    );

    if (!variant) {
      return null;
    }

    return getPricesForVariant(variant);
  };

  return {
    cheapestPrice: cheapestPrice(),
    product,
    variantPrice: variantPrice(),
  };
}
