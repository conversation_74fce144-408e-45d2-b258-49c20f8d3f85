/**
 * 验证产品变体价格的工具函数
 * 用于在添加到购物车前检查价格是否存在
 */

interface VariantPriceValidation {
  isValid: boolean;
  price?: {
    amount: number;
    currency_code: string;
    formatted_price: string;
  };
  error?: string;
}

/**
 * 验证变体是否有有效的价格（基于变体对象）
 * @param variant 变体对象
 * @returns 价格验证结果
 */
export function validateVariantPriceFromObject(variant: any): VariantPriceValidation {
  try {
    // 检查 calculated_price
    if (variant?.calculated_price?.calculated_amount && variant.calculated_price.calculated_amount > 0) {
      return {
        isValid: true,
        price: {
          amount: variant.calculated_price.calculated_amount,
          currency_code: variant.calculated_price.currency_code || 'usd',
          formatted_price: variant.calculated_price.formatted_amount || `$${(variant.calculated_price.calculated_amount / 100).toFixed(2)}`,
        },
      };
    }

    // 检查 prices 数组
    if (variant?.prices && Array.isArray(variant.prices) && variant.prices.length > 0) {
      const validPrice = variant.prices.find((price: any) => price.amount && price.amount > 0);
      if (validPrice) {
        return {
          isValid: true,
          price: {
            amount: validPrice.amount,
            currency_code: validPrice.currency_code || 'usd',
            formatted_price: `$${(validPrice.amount / 100).toFixed(2)}`,
          },
        };
      }
    }

    // 检查直接的 price 对象
    if (variant?.price?.amount && variant.price.amount > 0) {
      return {
        isValid: true,
        price: {
          amount: variant.price.amount,
          currency_code: variant.price.currency_code || 'usd',
          formatted_price: `$${(variant.price.amount / 100).toFixed(2)}`,
        },
      };
    }

    return {
      isValid: false,
      error: `Variant has no valid price configured`,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Error validating variant price: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * 验证变体是否有有效的价格（通过ID查询）
 * @param variantId 变体ID
 * @returns 价格验证结果
 */
export async function validateVariantPrice(variantId: string): Promise<VariantPriceValidation> {
  // 注意：这个函数现在主要用于日志记录，实际验证应该使用 validateVariantPriceFromObject
  console.warn(`validateVariantPrice called for ${variantId} - consider using validateVariantPriceFromObject instead`);

  return {
    isValid: false,
    error: `API-based validation not implemented - use validateVariantPriceFromObject instead`,
  };
}

/**
 * 批量验证多个变体的价格
 * @param variantIds 变体ID数组
 * @returns 价格验证结果映射
 */
export async function validateVariantPricesBatch(variantIds: string[]): Promise<Map<string, VariantPriceValidation>> {
  const results = new Map<string, VariantPriceValidation>();
  
  // 并行验证所有变体
  const promises = variantIds.map(async (variantId) => {
    const validation = await validateVariantPrice(variantId);
    results.set(variantId, validation);
  });

  await Promise.all(promises);
  return results;
}

/**
 * 检查产品变体是否可以添加到购物车
 * @param variant 产品变体对象
 * @returns 是否可以添加到购物车
 */
export function canAddToCart(variant: any): boolean {
  // 检查是否有 calculated_price（真正的Medusa计算价格）
  if (variant?.calculated_price?.calculated_amount && variant.calculated_price.calculated_amount > 0) {
    return true;
  }

  // 如果只有fallback价格，不能添加到购物车
  // 因为Medusa无法正确处理这种情况
  return false;
}

/**
 * 检查产品变体是否可以添加到购物车（基于价格对象）
 * @param priceObject 从getProductPrice返回的价格对象
 * @returns 是否可以添加到购物车
 */
export function canAddToCartFromPriceObject(priceObject: any): boolean {
  // 如果是fallback价格，不能添加到购物车
  if (priceObject?.is_fallback) {
    return false;
  }

  // 如果有正常的计算价格，可以添加到购物车
  if (priceObject?.calculated_price_number && priceObject.calculated_price_number > 0) {
    return true;
  }

  return false;
}

/**
 * 获取变体的显示价格
 * @param variant 产品变体对象
 * @returns 格式化的价格字符串或null
 */
export function getVariantDisplayPrice(variant: any): string | null {
  // 优先使用 calculated_price
  if (variant?.calculated_price?.calculated_amount) {
    const amount = variant.calculated_price.calculated_amount;
    const currency = variant.calculated_price.currency_code || 'USD';
    return `$${(amount / 100).toFixed(2)} ${currency.toUpperCase()}`;
  }

  // 使用 prices 数组中的第一个价格
  if (variant?.prices && Array.isArray(variant.prices) && variant.prices.length > 0) {
    const price = variant.prices[0];
    if (price.amount) {
      const currency = price.currency_code || 'USD';
      return `$${(price.amount / 100).toFixed(2)} ${currency.toUpperCase()}`;
    }
  }

  // 使用直接的 price 对象
  if (variant?.price?.amount) {
    const currency = variant.price.currency_code || 'USD';
    return `$${(variant.price.amount / 100).toFixed(2)} ${currency.toUpperCase()}`;
  }

  return null;
}
