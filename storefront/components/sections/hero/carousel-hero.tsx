"use client";

import {Link} from "@/components/shared/button";
import Heading from "@/components/shared/typography/heading";
import Label from "@/components/shared/typography/label";
import {imageBuilder} from "@/data/sanity/client";
import {useCarouselTextAnimation} from "@/hooks/use-carousel-text-animation";
import {cx} from "cva";
import {useState, useEffect} from "react";

import type {ModularPageSection} from "../types";

type Props = ModularPageSection<"section.hero">;

export default function CarouselHero(props: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const slides = props.carouselImages || [];
  const { getAnimatedClasses, animationKey } = useCarouselTextAnimation(currentIndex);

  // Auto-advance carousel every 5 seconds
  useEffect(() => {
    if (slides.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  if (!slides || slides.length === 0) {
    return null;
  }

  const currentSlide = slides[currentIndex];

  return (
    <div className="relative overflow-hidden rounded-lg">
      <div
        className="relative h-full w-full overflow-hidden rounded-lg"
        style={{
          height: "100vh",
        }}
      >
        {/* Carousel Images */}
        <div className="relative h-full w-full">
          {slides.map((slide, index) => (
            <div
              key={index}
              className={cx(
                "absolute inset-0 carousel-fade-transition",
                {
                  "opacity-100 carousel-slide-enter-active": index === currentIndex,
                  "opacity-0 carousel-slide-exit-active": index !== currentIndex,
                },
              )}
            >
              {slide?.image?.asset && (
                <img
                  alt={slide.title || "Carousel image"}
                  className="h-full w-full object-cover object-center"
                  loading={index === 0 ? "eager" : "lazy"}
                  src={imageBuilder
                    .image(slide.image)
                    .width(1200)
                    .height(675)
                    .auto("format")
                    .url()}
                />
              )}
            </div>
          ))}
        </div>

        {/* Navigation Arrows */}
        {slides.length > 1 && (
          <>
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full text-gray-200 transition-all hover:scale-110 hover:text-gray-400"
              aria-label="Previous image"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full  text-gray-200 transition-all hover:scale-110 hover:text-gray-400"
              aria-label="Next image"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </>
        )}

        {/* Dots Indicator */}
        {/* {slides.length > 1 && (
          <div className="absolute bottom-6 left-1/2 z-10 flex -translate-x-1/2 space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cx("h-3 w-3 rounded-full transition-all", {
                  "bg-white": index === currentIndex,
                  "bg-white/50 hover:bg-white/75": index !== currentIndex,
                })}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )} */}
      </div>

      {/* Mokuomo-style overlay with gradient */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

      <div className="absolute bottom-0 left-1/2 z-10 flex w-full -translate-x-1/2 flex-col items-center justify-center gap-6 text-balance px-s py-2xl text-center lg:max-w-[680px] lg:py-6xl">
        {/* Mokuomo-style tagline */}
        <Label
          className={cx(
            "tracking-wider text-white/90",
            getAnimatedClasses(0)
          )}
          desktopSize="base"
          font="sans"
          mobileSize="sm"
          key={`tagline-${animationKey}`}
        >
          Craft • Wood • Nature
        </Label>

        <Heading
          className={cx(
            "!leading-[110%] text-white drop-shadow-lg",
            getAnimatedClasses(100)
          )}
          desktopSize="6xl"
          font="serif"
          mobileSize="3xl"
          tag="h1"
          key={`title-${animationKey}`}
        >
          {currentSlide?.title || props.title}
        </Heading>

        {currentSlide?.subtitle && (
          <Label
            className={cx(
              "text-white/80 drop-shadow-lg",
              getAnimatedClasses(200)
            )}
            desktopSize="lg"
            font="sans"
            mobileSize="base"
            key={`subtitle-${animationKey}`}
          >
            {currentSlide.subtitle}
          </Label>
        )}

        {currentSlide?.cta?.link && (
          <Link
            href={currentSlide.cta.link}
            prefetch
            size="lg"
            variant="primary"
            className={cx(
              "mt-4 shadow-lg",
              getAnimatedClasses(300)
            )}
            key={`cta-${animationKey}`}
          >
            {currentSlide.cta.label}
          </Link>
        )}
      </div>
    </div>
  );
}
