import type {ModularPageSection} from "./types";

import CarouselSection from "../shared/carousel-section";
import Body from "../shared/typography/body";
import Heading from "../shared/typography/heading";
import Label from "../shared/typography/label";

export default function Testimonials(
  props: ModularPageSection<"section.testimonials">,
) {
  const slides = props.testimonials?.map((testimonial) => (
    <TestimonialCard
      author={testimonial.author}
      key={testimonial._id}
      text={testimonial.quote}
    />
  ));
  return (
    <section
      {...props.rootHtmlAttributes}
      className="w-full px-4 py-xl lg:px-8 lg:py-2xl"
    >
      <CarouselSection
        showButtons={true}
        showProgress={false}
        slides={slides}
        title={
          <Heading
            className="text-left mb-8"
            desktopSize="3xl"
            font="serif"
            mobileSize="2xl"
            tag="h2"
          >
            {props.title}
          </Heading>
        }
      />
    </section>
  );
}

function TestimonialCard(props: {
  author: string | undefined;
  text: string | undefined;
}) {
  return (
    <div className="mokuomo-card flex h-full min-h-[300px] w-[88vw] max-w-[450px] flex-col items-start justify-between gap-6 rounded-lg bg-background-warm p-8 lg:min-h-[350px] carousel-fade-transition">
      <Body
        className="italic leading-relaxed text-text-primary text-fade-transition"
        desktopSize="lg"
        font="serif"
        mobileSize="base"
      >
        "{props.text}"
      </Body>
      <div className="flex items-center gap-2 text-fade-transition text-delay-100">
        <Label
          className="text-text-secondary"
          font="sans"
          mobileSize="sm"
        >
          —
        </Label>
        <Label
          className="font-medium text-text-primary"
          font="sans"
          mobileSize="sm"
        >
          {props.author}
        </Label>
      </div>
    </div>
  );
}
