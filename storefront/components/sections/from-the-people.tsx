"use client";

import {imageBuilder} from "@/data/sanity/client";
import {useCarouselTextAnimation} from "@/hooks/use-carousel-text-animation";
import {cx} from "cva";
import {useEffect, useState} from "react";

import type {ModularPageSection} from "./types";

import Body from "../shared/typography/body";
import Label from "../shared/typography/label";

export default function FromThePeople(
  props: ModularPageSection<"section.fromThePeople">,
) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const testimonials = props.testimonials || [];
  const { animationKey, getAnimatedClasses } = useCarouselTextAnimation(currentIndex);

  // Auto-advance testimonials every 5 seconds
  useEffect(() => {
    if (testimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  if (!testimonials || testimonials.length === 0) {
    return null;
  }

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section
      {...props.rootHtmlAttributes}
      className="w-full bg-white py-16"
    >
      <div className="container mx-auto px-4">
        {/* Section Title */}
        <div className="mb-12 text-center">
          <Label
            className="text-gray-600"
            desktopSize="base"
            font="sans"
            mobileSize="sm"
          >
            {props.title || "From The People"}
          </Label>
        </div>

        {/* Testimonial Content */}
        <div className="flex flex-col items-center lg:flex-row lg:items-stretch lg:gap-12 min-h-[500px] lg:min-h-[600px]">
          {/* Text Content */}
          <div className="flex w-full flex-col items-center justify-center text-center lg:w-1/2 min-h-[400px] lg:min-h-[500px]">
            {/* Subtitle */}
            <Label
              className={cx(
                "mb-8 text-gray-600",
                getAnimatedClasses(0)
              )}
              desktopSize="base"
              font="sans"
              mobileSize="sm"
              key={`subtitle-${animationKey}`}
            >
              From The People
            </Label>

            {/* Star Rating */}
            <div
              className={cx(
                "mb-6 flex items-center justify-center gap-1",
                getAnimatedClasses(100)
              )}
              key={`rating-${animationKey}`}
            >
              {[...Array(currentTestimonial.rating || 5)].map((_, index) => (
                <svg
                  key={index}
                  aria-hidden="true"
                  focusable="false"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="text-yellow-400"
                >
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                </svg>
              ))}
            </div>

            {/* Testimonial Text */}
            <div className="mb-8 flex min-h-[120px] lg:min-h-[140px] items-center justify-center">
              <Body
                className={cx(
                  "max-w-lg italic leading-relaxed text-gray-800 text-center",
                  getAnimatedClasses(200)
                )}
                desktopSize="xl"
                font="serif"
                key={`quote-${animationKey}`}
                mobileSize="lg"
              >
                &ldquo;{currentTestimonial.quote}&rdquo;
              </Body>
            </div>

            {/* Author */}
            <Label
              className={cx(
                "text-gray-600",
                getAnimatedClasses(300)
              )}
              desktopSize="base"
              font="sans"
              key={`author-${animationKey}`}
              mobileSize="sm"
            >
              — {currentTestimonial.author}
            </Label>
          </div>

          {/* Product Image */}
          <div className="mt-8 w-full lg:mt-0 lg:w-1/2">
            {currentTestimonial.productImage && (
              <figure
                className="group"
                key={`image-${animationKey}`}
              >
                <a
                  className="block overflow-hidden rounded-lg"
                  href={currentTestimonial.productLink || "#"}
                >
                  <img
                    alt={currentTestimonial.productTitle || 'Product image'}
                    className={cx(
                      "aspect-square w-full object-cover transition-all duration-500 ease-in-out group-hover:scale-105",
                      "animate-image-fade"
                    )}
                    key={`img-${animationKey}`}
                    loading="eager"
                    src={imageBuilder.image(currentTestimonial.productImage).width(600).height(600).auto('format').url()}
                  />
                </a>
                {currentTestimonial.productTitle && (
                  <figcaption
                    className={cx(
                      "mt-4 text-center",
                      getAnimatedClasses(400)
                    )}
                    key={`caption-${animationKey}`}
                  >
                    <a
                      className="inline-flex items-center text-gray-800 underline decoration-1 underline-offset-4 transition-colors hover:text-gray-600"
                      href={currentTestimonial.productLink || "#"}
                    >
                      Shop {currentTestimonial.productTitle}
                    </a>
                  </figcaption>
                )}
              </figure>
            )}
          </div>
        </div>

        {/* Navigation Controls */}
        {testimonials.length > 1 && (
          <div className="mt-12 flex items-center justify-center gap-4">
            <button
              onClick={prevTestimonial}
              className="flex h-10 w-10 items-center justify-center text-gray-400 transition-colors hover:text-gray-600"
              aria-label="Previous testimonial"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>

            <button
              onClick={nextTestimonial}
              className="flex h-10 w-10 items-center justify-center text-gray-400 transition-colors hover:text-gray-600"
              aria-label="Next testimonial"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
        )}
      </div>
    </section>
  );
}
