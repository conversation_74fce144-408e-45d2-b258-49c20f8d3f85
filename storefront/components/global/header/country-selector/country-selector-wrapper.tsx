"use client";

import {useEffect, useState} from "react";
import {cx} from "cva";
import Body from "@/components/shared/typography/body";
import {
  CloseDialog,
  OpenDialog,
  SideDialog,
} from "@/components/shared/side-dialog";
import {Dialog, Title} from "@radix-ui/react-dialog";
import Icon from "@/components/shared/icon";
import Heading from "@/components/shared/typography/heading";
import Link from "next/link";
import {usePathname} from "next/navigation";
import {useCountryCode} from "@/components/context/country-code-context";
import config from "@/config";
import {Suspense} from "react";

type Country = {
  code: string;
  currency: {
    code: string;
    symbol: string;
  };
  name: string;
};

export default function CountrySelectorWrapper({
  isScrolled = false,
  isHomePage = false,
}: {
  isScrolled?: boolean;
  isHomePage?: boolean;
}) {
  const [countries, setCountries] = useState<Country[]>([]);
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const countryCode = useCountryCode();

  useEffect(() => {
    // Load countries from API route
    fetch("/api/countries")
      .then((res) => res.json())
      .then((data) => {
        setCountries(data as Country[]);
      })
      .catch((error) => {
        console.error("Error loading countries:", error);
        setCountries([]);
      });
  }, []);

  const getNewPath = (newCountryCode: string) => {
    const pathParts = pathname.split("/");

    const isDefault = newCountryCode === config.defaultCountryCode;
    const currentIsDefault = countryCode === config.defaultCountryCode;

    if (isDefault && !currentIsDefault) {
      pathParts.splice(1, 1);
    } else if (!isDefault && currentIsDefault) {
      pathParts.splice(1, 0, newCountryCode);
    } else if (!isDefault && !currentIsDefault) {
      pathParts[1] = newCountryCode;
    }

    return pathParts.join("/");
  };

  const selectedCountry =
    countries.find((country) => country?.code === countryCode) ||
    countries.find((country) => country?.code) ||
    { code: 'us', currency: { code: 'USD', symbol: '$' }, name: 'United States' };

  return (
    <Dialog onOpenChange={(v) => setOpen(v)} open={open}>
      <OpenDialog>
        <Body
          className={cx(
            "overflow-hidden whitespace-nowrap rounded-lg border-[1.5px] p-2 lg:border-none transition-all duration-300",
            {
              // 只在首页时使用白色文字和边框效果
              "border-white/30 text-white": isHomePage && !isScrolled,
              "border-accent text-text-primary": !isHomePage || isScrolled,
            }
          )}
          font="sans"
          mobileSize="lg"
        >
          {selectedCountry?.code?.toUpperCase() || 'US'} [
          {selectedCountry?.currency?.symbol || '$'}]
        </Body>
      </OpenDialog>
      <SideDialog>
        <div className="relative flex h-full w-full flex-col border-l border-accent bg-background">
          <div className="flex h-full w-full flex-col bg-background p-s pr-xs">
            <Title asChild>
              <Heading
                className="py-4"
                desktopSize="lg"
                font="serif"
                mobileSize="base"
                tag="h2"
              >
                Select your country
              </Heading>
            </Title>
            <CloseDialog
              aria-label="Close"
              className="absolute right-[10px] top-[10px]"
            >
              <Icon className="h-9 w-9" name="Close" />
            </CloseDialog>
            <div className="flex flex-1 flex-col items-stretch overflow-y-scroll">
              {countries.filter(country => country?.code).map((country) => (
                <Suspense key={country.code}>
                  <Link
                    className="flex items-center justify-between border-b border-accent p-4 transition-colors hover:bg-secondary"
                    href={getNewPath(country.code)}
                    onClick={() => setOpen(false)}
                  >
                    <Body font="sans" mobileSize="lg">
                      {country.name}
                    </Body>
                    <Body font="sans" mobileSize="base">
                      {country.code.toUpperCase()} [{country.currency.symbol}]
                    </Body>
                  </Link>
                </Suspense>
              ))}
            </div>
          </div>
        </div>
      </SideDialog>
    </Dialog>
  );
}
