"use client";
import Icon from "@/components/shared/icon";
import {OpenDialog} from "@/components/shared/side-dialog";
import Body from "@/components/shared/typography/body";
import {cx} from "cva";

import {useCart} from "./cart-context";

export default function OpenCart({isScrolled = false, isHomePage = false}: {isScrolled?: boolean; isHomePage?: boolean}) {
  const {cart} = useCart();

  const count = (cart?.items?.length || 0).toFixed();

  return (
    <OpenDialog>
      <div className="relative h-10 w-10 p-2">
        <Icon
          name="Cart"
          className={cx(
            "transition-colors duration-300",
            {
              // 只在首页时使用白色图标效果
              "text-white": isHomePage && !isScrolled,
              "text-text-primary": !isHomePage || isScrolled,
            }
          )}
        />
        <Body
          className="absolute right-0 top-0 flex h-5 w-5 items-center justify-center rounded-full bg-accent text-background"
          font="sans"
          mobileSize="sm"
        >
          {count}
        </Body>
      </div>
    </OpenDialog>
  );
}
