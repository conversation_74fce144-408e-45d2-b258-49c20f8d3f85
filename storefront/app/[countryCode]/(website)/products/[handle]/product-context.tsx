"use client";

import type {StoreProduct, StoreProductVariant} from "@medusajs/types";
import type {PropsWithChildren} from "react";

import {parseAsString, useQueryStates} from "nuqs";
import React, {createContext, useContext} from "react";

interface ProductVariantsContextType {
  activeVariant: StoreProductVariant | undefined;
  selectedOptions: Record<string, string | undefined>;
  setSelectedOptions: React.Dispatch<
    React.SetStateAction<Record<string, string | undefined>>
  >;
}

const ProductVariantsContext = createContext<
  ProductVariantsContextType | undefined
>(undefined);

export function ProductVariantsProvider({
  children,
  product,
}: PropsWithChildren<{
  product: StoreProduct;
}>) {
  // 计算每个选项的默认值，优先选择第一个有效的变体组合
  const getDefaultOptions = () => {
    if (!product.variants || product.variants.length === 0) {
      return Object.fromEntries(
        product.options?.map((option) => [
          option.title.toLowerCase(),
          parseAsString.withDefault(
            option.values?.[0]?.value.toLowerCase() ?? "",
          ),
        ]) ?? [],
      );
    }

    // 使用第一个变体的选项作为默认值
    const firstVariant = product.variants[0];
    const defaultOptions: Record<string, any> = {};

    product.options?.forEach((option) => {
      const variantOption = firstVariant.options?.find(
        (vo) => vo.option?.title === option.title
      );

      defaultOptions[option.title.toLowerCase()] = parseAsString.withDefault(
        variantOption?.value.toLowerCase() ?? option.values?.[0]?.value.toLowerCase() ?? "",
      );
    });

    return defaultOptions;
  };

  const [selectedOptions, setSelectedOptions] = useQueryStates(
    getDefaultOptions(),
    {
      history: "push",
    },
  );

  const activeVariant = product.variants?.find((variant) => {
    return variant?.options?.every(
      ({option, value}) =>
        selectedOptions[option?.title.toLowerCase() || ""] ===
        value.toLowerCase(),
    );
  });

  // 如果没有找到匹配的变体，不要回退到第一个变体
  // 这样可以避免显示错误的价格和选项组合

  const activeVariantWithProduct = !activeVariant
    ? activeVariant
    : {...activeVariant, product};

  return (
    <ProductVariantsContext.Provider
      value={{
        activeVariant: activeVariantWithProduct,
        selectedOptions,
        setSelectedOptions,
      }}
    >
      {children}
    </ProductVariantsContext.Provider>
  );
}

export function useProductVariants() {
  const context = useContext(ProductVariantsContext);
  if (context === undefined) {
    throw new Error(
      "useProductVariants must be used within a ProductVariantsProvider",
    );
  }
  return context;
}
