"use client";
import type {ButtonProps} from "@/components/shared/button";
import type {StoreProductVariant} from "@medusajs/types";

import {addToCartEventBus} from "@/components/global/header/cart/event-bus";
import {Cta} from "@/components/shared/button";
import {track} from "@vercel/analytics";
import {canAddToCart} from "@/utils/medusa/validate-variant-price";
import {cx} from "cva";

import {useProductVariants} from "../product-context";

export default function AddToCart({
  region_id,
  variant,
}: {
  region_id: string;
  variant: "PDP" | "sticky";
}) {
  const {activeVariant} = useProductVariants();
  return (
    <AddToCartButton
      className={cx("", {
        "!h-[60px] w-fit": variant === "sticky",
        "w-full": variant === "PDP",
      })}
      label="Add to cart"
      productVariant={activeVariant}
      regionId={region_id}
      size={variant === "PDP" ? "xl" : "md"}
      variant={variant === "PDP" ? "outline" : "primary"}
    />
  );
}

type AddToCartButtonProps = {
  label: string;
  productVariant: StoreProductVariant | undefined;
  regionId: string;
} & Omit<ButtonProps, "onClick">;

export function AddToCartButton({
  label,
  productVariant,
  regionId,
  ...buttonProps
}: AddToCartButtonProps) {
  const handleAddToCart = () => {
    if (!productVariant) {
      alert("请选择有效的产品选项组合。\nPlease select a valid product option combination.");
      return;
    }

    // 验证产品变体是否有价格
    console.log('🔍 调试变体数据:', {
      id: productVariant.id,
      title: productVariant.title,
      calculated_price: productVariant.calculated_price,
      prices: (productVariant as any).prices,
      price: (productVariant as any).price,
    });

    // 使用简化的价格验证
    const hasValidPrice = canAddToCart(productVariant);
    console.log('💰 价格验证结果:', hasValidPrice);

    if (!hasValidPrice) {
      console.warn(`Cannot add variant ${productVariant.id} to cart: no valid calculated_price`, {
        calculated_price: productVariant.calculated_price,
        variant: productVariant,
      });

      // 显示用户友好的错误消息
      alert("此产品价格计算有问题，无法添加到购物车。请联系客服。\nThis product has price calculation issues and cannot be added to cart. Please contact support.");
      return;
    }

    console.log(`✅ Adding variant ${productVariant.id} to cart with calculated_price:`, productVariant.calculated_price?.calculated_amount);

    addToCartEventBus.emitCartAdd({
      productVariant,
      regionId,
    });

    track("add-to-cart", {
      quantity: 1,
      region_id: regionId,
      variantId: productVariant.id,
    });
  };

  // 检查是否应该禁用按钮
  const hasPrice = productVariant ? canAddToCart(productVariant) : false;
  const isDisabled = !addToCartEventBus.handler || !productVariant || !hasPrice;

  return (
    <Cta
      {...buttonProps}
      disabled={isDisabled}
      onClick={(e) => {
        e.preventDefault();
        if (productVariant && hasPrice) {
          handleAddToCart();
        }
      }}
    >
      {label}
    </Cta>
  );
}
