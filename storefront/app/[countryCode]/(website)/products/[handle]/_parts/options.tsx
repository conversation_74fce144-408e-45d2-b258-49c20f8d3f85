"use client";

import type {StoreProductOption} from "@medusajs/types";

import Select from "@/components/shared/select";
import {useMemo} from "react";

import {useProductVariants} from "../product-context";

type Props = {
  options: StoreProductOption[];
  product: any; // 添加product参数以获取变体信息
};

export default function OptionsSelect({options, product}: Props) {
  const {selectedOptions, setSelectedOptions} = useProductVariants();

  // 计算每个选项的可用值（只显示实际存在变体的选项）
  const availableOptions = useMemo(() => {
    if (!product?.variants || !options) return [];

    console.log('🔧 计算可用选项:', {
      variants: product.variants.length,
      options: options.length,
      selectedOptions,
    });

    return options.map((option) => {
      // 获取当前选项的所有可能值
      const allValues = option.values?.map(({value}) => value) || [];

      console.log(`📋 处理选项 ${option.title}:`, {
        allValues,
        selectedOptions,
      });

      // 进一步过滤：考虑当前已选择的其他选项
      const compatibleValues = allValues.filter((value) => {
        // 创建一个临时的选项组合，包含当前测试的值
        const testOptions = {
          ...selectedOptions,
          [option.title.toLowerCase()]: value.toLowerCase(),
        };

        // 检查是否存在匹配这个选项组合的变体
        const hasMatchingVariant = product.variants.some((variant: any) => {
          const matches = variant.options?.every((variantOption: any) => {
            const optionTitle = variantOption.option?.title?.toLowerCase();
            const variantValue = variantOption.value?.toLowerCase();
            const selectedValue = testOptions[optionTitle];

            return selectedValue === variantValue;
          });

          if (matches) {
            console.log(`✅ 找到匹配变体 ${variant.id} for ${option.title}=${value}:`, {
              variantOptions: variant.options?.map((vo: any) => `${vo.option?.title}=${vo.value}`),
              testOptions,
            });
          }

          return matches;
        });

        if (!hasMatchingVariant) {
          console.log(`❌ 没有匹配变体 for ${option.title}=${value}:`, testOptions);
        }

        return hasMatchingVariant;
      });

      console.log(`🎯 ${option.title} 可用值:`, compatibleValues);

      return {
        ...option,
        availableValues: compatibleValues,
      };
    });
  }, [options, product?.variants, selectedOptions]);

  return availableOptions?.map((option) => {
    // 只显示有可用值的选项
    if (!option.availableValues || option.availableValues.length === 0) {
      return null;
    }

    // 如果只有一个可用值，也不显示选择器
    if (option.availableValues.length <= 1) {
      return null;
    }

    const values = option.availableValues.map((value) => ({
      label: value,
      value: value.toLowerCase(),
    }));

    const activeOption = selectedOptions[option.title.toLowerCase()];
    const setOption = (value: string) =>
      setSelectedOptions((prev) => ({
        ...prev,
        [option.title.toLowerCase()]: value,
      }));

    return (
      <Select
        className="w-fit"
        key={option.id}
        options={values}
        placeholder={activeOption}
        setOption={setOption}
        variant="outline"
      />
    );
  });
}
