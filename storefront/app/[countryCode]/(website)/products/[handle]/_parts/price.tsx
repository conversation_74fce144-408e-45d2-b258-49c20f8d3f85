"use client";
import type {StoreProduct} from "@medusajs/types";

import Body from "@/components/shared/typography/body";
import {getProductPrice} from "@/utils/medusa/get-product-price";

import {useProductVariants} from "../product-context";

export default function Price({
  product,
}: {
  product: Pick<StoreProduct, "id" | "variants">;
}) {
  const {activeVariant, selectedOptions} = useProductVariants();

  const {cheapestPrice, variantPrice} = getProductPrice({
    product,
    variantId: activeVariant?.id,
  });

  // 调试日志
  console.log('Price component debug:', {
    activeVariantId: activeVariant?.id,
    selectedOptions,
    variantPrice,
    cheapestPrice,
    hasVariantPrice: !!variantPrice?.calculated_price,
    hasCheapestPrice: !!cheapestPrice?.calculated_price,
    hasActiveVariant: !!activeVariant,
  });

  // 检查是否没有匹配的变体（无效的选项组合）
  if (!activeVariant) {
    return (
      <div className="flex flex-col gap-1">
        <Body className="text-red-400" desktopSize="sm" font="sans" mobileSize="xs">
          Invalid option combination
        </Body>
      </div>
    );
  }

  // 检查是否使用了fallback价格
  const isUsingFallback = variantPrice?.is_fallback || cheapestPrice?.is_fallback;

  // 如果没有价格信息，显示适当的消息
  if (!variantPrice?.calculated_price && !cheapestPrice?.calculated_price) {
    return (
      <div className="flex flex-col gap-1">
        <Body className="text-gray-400" desktopSize="sm" font="sans" mobileSize="xs">
          Price not set
        </Body>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-1">
      {/* {variantPrice?.calculated_price} */}
      {!isUsingFallback && <Body className={isUsingFallback ? "text-orange-600" : ""} desktopSize="xl" font="sans" mobileSize="lg">
        {variantPrice?.calculated_price ? (
          variantPrice.calculated_price
        ) : (
          <>from {cheapestPrice?.calculated_price}</>
        )}
      </Body>}
      {isUsingFallback && (
        <Body className="text-orange-500" desktopSize="xs" font="sans" mobileSize="xs">
          Price not set
        </Body>
      )}
    </div>
  );
}
