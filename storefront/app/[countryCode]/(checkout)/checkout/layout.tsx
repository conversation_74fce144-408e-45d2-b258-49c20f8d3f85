import type {PageProps} from "@/types";
import type {Metadata} from "next";
import type {PropsWithChildren} from "react";

import Footer from "@/components/global/footer";
import LocalizedLink from "@/components/shared/localized-link";
import config from "@/config";
import {loadGlobalData} from "@/data/sanity";
import {getOgImages} from "@/data/sanity/resolve-sanity-route-metadata";

// 内联的防止后退导航组件
function PreventBackNavigationSmoothScroll() {
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
          (function() {
            // 启用平滑滚动
            if (typeof window !== 'undefined') {
              document.documentElement.style.scrollBehavior = 'smooth';
            }

            // 防止后退导航
            function handlePopState() {
              window.history.pushState(null, '', window.location.href);
            }

            // 页面卸载前的警告
            function handleBeforeUnload(event) {
              const message = "Are you sure you want to leave? Your checkout progress may be lost.";
              event.returnValue = message;
              return message;
            }

            // 初始化
            window.history.pushState(null, '', window.location.href);
            window.addEventListener('popstate', handlePopState);
            window.addEventListener('beforeunload', handleBeforeUnload);
          })();
        `,
      }}
    />
  );
}

type LayoutProps = PropsWithChildren<
  Omit<PageProps<"countryCode">, "searchParams">
>;

export async function generateMetadata(): Promise<Metadata> {
  const data = await loadGlobalData();

  return {
    openGraph: {
      images: !data?.fallbackOGImage
        ? undefined
        : getOgImages(data.fallbackOGImage),
      title: config.siteName,
    },
    title: config.siteName,
  };
}

export default async function Layout(props: LayoutProps) {
  const {children} = props;

  const data = await loadGlobalData();

  return (
    <>
      <PreventBackNavigationSmoothScroll />
      <div className="sticky top-0 z-[20] w-screen bg-background">
        <div className="my-s w-full bg-background px-4 lg:px-8">
          <LocalizedLink href="/" prefetch>
            <span className="text-2xl max-md:text-base">Wood Carving</span>
          </LocalizedLink>
        </div>
        {/* <BottomBorder /> */}
      </div>
      <main className="flex-1">{children}</main>
      {data.footer && <Footer variant="simple" {...data.footer} />}
    </>
  );
}
